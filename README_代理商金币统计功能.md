# 代理商金币统计功能

## 功能概述
为代理商后台添加金币统计信息显示，包括：
- **总计金币**：代理商累计获得的所有金币
- **开户使用金币**：用于开户业务消耗的金币  
- **剩余金币**：当前可用的金币余额

## 显示效果
```
总计金币 1000个；开户使用金币 300个，剩余金币 700个
```

## 新增的文件

### 1. GoldenBeanStatsVO.java
**路径**: `jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/user_front/vo/GoldenBeanStatsVO.java`
- 金币统计信息的数据传输对象
- 包含总计金币、开户使用金币、剩余金币、统计时间等字段

## 修改的文件

### 1. IInzUserFrontService.java
- 添加了 `getGoldenBeanStats(String userId)` 方法声明

### 2. InzUserFrontServiceImpl.java  
- 实现了金币统计方法
- 包含三个辅助方法：
  - `getTotalGoldenBean()` - 获取总计金币
  - `getUsedGoldenBean()` - 获取开户使用金币
  - `getRemainingGoldenBean()` - 获取剩余金币

### 3. InzUserFrontController.java
- 添加了 `/getGoldenBeanStats` 接口
- 包含完整的权限验证和异常处理

## 接口信息

### 请求信息
- **方法**: GET
- **路径**: `/user_front/inzUserFront/getGoldenBeanStats`
- **权限**: 需要代理商角色权限
- **参数**: 无（从登录用户信息获取）

### 返回数据
```json
{
  "success": true,
  "result": {
    "totalGoldenBean": 1000,
    "usedGoldenBean": 300,
    "remainingGoldenBean": 700,
    "statsTime": "2025-08-01 10:30:00"
  }
}
```

## 权限控制

### 支持的角色
只有以下代理商角色可以查看金币统计：
- `chuang` - 创总
- `channel` - 渠道
- `area_partner` - 区/县合伙人
- `city_partner` - 市级合伙人
- `province_partner` - 省级合伙人

### 数据权限
- 每个代理商只能查看自己的金币统计
- 通过手机号关联后台用户和前台用户
- 严格的权限验证，防止数据泄露

## 统计逻辑

### 1. 总计金币
- 统计 `inz_user_pay_log` 表中所有 `type=1`（收入）的金币总和
- 包括：推荐奖励、系统奖励、管理员充值等

### 2. 开户使用金币
- 统计 `inz_user_pay_log` 表中所有 `type=0`（支出）且内容包含"开通"或"开户"的金币总和
- 主要是为其他用户开通课程消耗的金币

### 3. 剩余金币
- 直接从 `inz_user_front` 表获取当前的 `goldenBean` 字段值
- 这是实时的可用金币余额

## 异常处理

### 异常情况
1. **用户无权限**: 返回权限错误信息
2. **用户不存在**: 返回用户不存在错误
3. **数据查询异常**: 返回默认统计数据（全部为0）
4. **系统异常**: 记录错误日志，返回通用错误信息

### 降级策略
- 单个统计项查询失败时，该项返回0，不影响其他统计
- 数据库异常时，返回默认统计信息
- 确保接口始终有响应，不会因为异常导致前端报错

## 测试方法

### 1. 代理商用户测试
```bash
# 使用代理商账号登录
curl -X GET "http://localhost:8080/user_front/inzUserFront/getGoldenBeanStats" \
  -H "X-Access-Token: YOUR_AGENT_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. 权限测试
```bash
# 使用普通用户账号登录（应该返回权限错误）
curl -X GET "http://localhost:8080/user_front/inzUserFront/getGoldenBeanStats" \
  -H "X-Access-Token: YOUR_NORMAL_USER_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. 验证统计准确性
1. 查看代理商的金币记录
2. 手动计算总计金币、开户使用金币
3. 对比接口返回的统计数据

## 前端集成建议

### 1. 调用接口
```javascript
// 获取金币统计信息
async function getGoldenBeanStats() {
  try {
    const response = await axios.get('/user_front/inzUserFront/getGoldenBeanStats');
    if (response.data.success) {
      return response.data.result;
    } else {
      console.error('获取金币统计失败:', response.data.message);
      return null;
    }
  } catch (error) {
    console.error('接口调用失败:', error);
    return null;
  }
}
```

### 2. 显示统计信息
```javascript
// 显示金币统计
function displayGoldenBeanStats(stats) {
  if (!stats) {
    return '暂无金币统计信息';
  }
  
  const { totalGoldenBean, usedGoldenBean, remainingGoldenBean } = stats;
  return `总计金币 ${totalGoldenBean}个；开户使用金币 ${usedGoldenBean}个，剩余金币 ${remainingGoldenBean}个`;
}
```

### 3. 界面布局建议
在代理商用户列表页面顶部添加统计信息卡片：
```html
<div class="golden-bean-stats-card">
  <h3>金币统计</h3>
  <p>{{ goldenBeanStatsText }}</p>
</div>
```

## 性能考虑

### 1. 查询优化
- 使用索引优化数据库查询
- 分别查询三个统计项，避免复杂的联合查询
- 考虑对热点数据进行缓存

### 2. 响应时间
- 目标响应时间 < 1秒
- 添加性能监控和告警
- 异常情况下的快速降级

## 部署说明

### 1. 数据库准备
- 确认 `inz_user_front` 表的 `golden_bean` 字段正常
- 确认 `inz_user_pay_log` 表的数据完整性
- 建议添加相关索引提升查询性能

### 2. 应用部署
1. 部署修改后的代码
2. 重启应用服务
3. 验证接口功能正常
4. 监控接口性能和错误日志

### 3. 验证步骤
1. 使用代理商账号登录
2. 调用金币统计接口
3. 验证返回数据的准确性
4. 检查权限控制是否正常

## 注意事项

### 1. 数据一致性
- 确保金币记录与实际余额保持一致
- 定期核对统计数据的准确性
- 监控异常的金币变动

### 2. 权限安全
- 严格控制接口访问权限
- 记录敏感操作的访问日志
- 防止数据泄露和越权访问

### 3. 性能监控
- 监控接口响应时间
- 关注数据库查询性能
- 设置合理的告警阈值

## 联系信息
如有问题，请联系开发团队：
- 产品经理：Emma
- 架构师：Bob
- 开发工程师：Alex
