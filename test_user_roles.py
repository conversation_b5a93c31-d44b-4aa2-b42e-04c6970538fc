#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户角色信息增强功能测试脚本
测试用户信息接口是否正确返回角色信息
"""

import requests
import json
import sys

class UserRoleTest:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = None
    
    def login(self, username="admin", password="123456"):
        """登录获取token"""
        login_url = f"{self.base_url}/sys/login"
        
        # 先获取验证码
        captcha_url = f"{self.base_url}/sys/randomImage"
        captcha_response = self.session.get(captcha_url)
        
        if captcha_response.status_code != 200:
            print("获取验证码失败")
            return False
        
        # 模拟登录（实际环境中需要真实的验证码）
        login_data = {
            "username": username,
            "password": password,
            "captcha": "1234",  # 测试环境可能不需要真实验证码
            "checkKey": "test"
        }
        
        response = self.session.post(login_url, json=login_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                self.token = result.get("result", {}).get("token")
                print(f"登录成功，token: {self.token[:20]}...")
                return True
            else:
                print(f"登录失败: {result.get('message')}")
                return False
        else:
            print(f"登录请求失败，状态码: {response.status_code}")
            return False
    
    def get_user_info(self):
        """获取用户信息"""
        if not self.token:
            print("请先登录")
            return None
        
        headers = {
            "X-Access-Token": self.token,
            "Content-Type": "application/json"
        }
        
        user_info_url = f"{self.base_url}/sys/user/getUserInfo"
        response = self.session.get(user_info_url, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                return result.get("result")
            else:
                print(f"获取用户信息失败: {result.get('message')}")
                return None
        else:
            print(f"获取用户信息请求失败，状态码: {response.status_code}")
            return None
    
    def test_user_roles(self):
        """测试用户角色信息"""
        print("=" * 50)
        print("开始测试用户角色信息功能")
        print("=" * 50)
        
        # 1. 登录
        print("\n1. 测试登录...")
        if not self.login():
            print("登录失败，测试终止")
            return False
        
        # 2. 获取用户信息
        print("\n2. 测试获取用户信息...")
        user_info = self.get_user_info()
        
        if not user_info:
            print("获取用户信息失败，测试终止")
            return False
        
        # 3. 验证角色信息
        print("\n3. 验证角色信息...")
        user_data = user_info.get("userInfo", {})
        
        print(f"用户名: {user_data.get('username')}")
        print(f"真实姓名: {user_data.get('realname')}")
        print(f"主要角色: {user_data.get('role')}")
        
        # 检查角色列表
        role_list = user_data.get("roleList", [])
        print(f"角色列表数量: {len(role_list)}")
        
        if role_list:
            print("角色详情:")
            for i, role in enumerate(role_list):
                print(f"  角色 {i+1}:")
                print(f"    ID: {role.get('id')}")
                print(f"    名称: {role.get('roleName')}")
                print(f"    代码: {role.get('roleCode')}")
                print(f"    描述: {role.get('description')}")
        else:
            print("⚠️  警告: 角色列表为空")
        
        # 4. 验证测试结果
        print("\n4. 测试结果验证...")
        
        success = True
        
        # 检查基础字段
        if not user_data.get("username"):
            print("❌ 用户名为空")
            success = False
        else:
            print("✅ 用户名正常")
        
        # 检查角色字段
        if "role" not in user_data:
            print("❌ 缺少role字段")
            success = False
        else:
            print("✅ role字段存在")
        
        # 检查角色列表字段
        if "roleList" not in user_data:
            print("❌ 缺少roleList字段")
            success = False
        else:
            print("✅ roleList字段存在")
            
            # 如果有角色，验证角色信息完整性
            if role_list:
                for i, role in enumerate(role_list):
                    if not all(key in role for key in ['id', 'roleName', 'roleCode']):
                        print(f"❌ 角色 {i+1} 信息不完整")
                        success = False
                    else:
                        print(f"✅ 角色 {i+1} 信息完整")
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 测试通过！用户角色信息功能正常")
        else:
            print("❌ 测试失败！存在问题需要修复")
        print("=" * 50)
        
        return success

def main():
    """主函数"""
    # 可以通过命令行参数指定服务器地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8080"
    
    tester = UserRoleTest(base_url)
    success = tester.test_user_roles()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
