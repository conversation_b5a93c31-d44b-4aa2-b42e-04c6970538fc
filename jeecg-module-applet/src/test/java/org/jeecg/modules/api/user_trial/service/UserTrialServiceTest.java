package org.jeecg.modules.api.user_trial.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.jeecg.modules.api.user_front.entity.UserFront;
import org.jeecg.modules.api.user_front.service.UserFrontService;
import org.jeecg.modules.api.user_trial.entity.InzUserTrialLog;
import org.jeecg.modules.api.user_trial.mapper.UserTrialMapper;
import org.jeecg.modules.api.user_trial.service.impl.UserTrialServiceImpl;
import org.jeecg.modules.api.user_trial.vo.TrialHistoryVO;
import org.jeecg.modules.api.user_trial.vo.TrialOpenResult;
import org.jeecg.modules.api.user_trial.vo.TrialStatusVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @Description: 用户试用管理Service单元测试
 * @Author: Alex (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
@ExtendWith(MockitoExtension.class)
class UserTrialServiceTest {

    @Mock
    private UserTrialMapper userTrialMapper;

    @Mock
    private UserFrontService userFrontService;

    @InjectMocks
    private UserTrialServiceImpl userTrialService;

    private UserFront testUser;
    private String testUserId = "test-user-001";

    @BeforeEach
    void setUp() {
        testUser = new UserFront();
        testUser.setId(testUserId);
        testUser.setRealName("测试用户");
        testUser.setPhone("13800138000");
        testUser.setStatus(1);
        testUser.setTrialTotalDays(0);
        testUser.setTrialRemainingDays(8);
        testUser.setTrialLastUpdate(new Date());
    }

    @Test
    void testOpenTrial_Success() {
        // Given
        Integer trialDays = 3;
        String remark = "首次试用";
        
        when(userFrontService.getById(testUserId)).thenReturn(testUser);
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(0);
        when(userTrialService.save(any(InzUserTrialLog.class))).thenReturn(true);
        when(userFrontService.updateById(any(UserFront.class))).thenReturn(true);

        // When
        TrialOpenResult result = userTrialService.openTrial(testUserId, trialDays, remark);

        // Then
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(trialDays, result.getTrialDays());
        assertEquals(5, result.getRemainingDays()); // 8 - 3 = 5
        assertNotNull(result.getStartDate());
        assertNotNull(result.getEndDate());
        assertEquals("试用开通成功", result.getMessage());
    }

    @Test
    void testOpenTrial_UserNotFound() {
        // Given
        when(userFrontService.getById(testUserId)).thenReturn(null);

        // When & Then
        Exception exception = assertThrows(Exception.class, () -> {
            userTrialService.openTrial(testUserId, 3, "测试");
        });
        assertTrue(exception.getMessage().contains("用户不存在"));
    }

    @Test
    void testOpenTrial_UserStatusInvalid() {
        // Given
        testUser.setStatus(0); // 停用状态
        when(userFrontService.getById(testUserId)).thenReturn(testUser);

        // When & Then
        Exception exception = assertThrows(Exception.class, () -> {
            userTrialService.openTrial(testUserId, 3, "测试");
        });
        assertTrue(exception.getMessage().contains("用户状态异常"));
    }

    @Test
    void testOpenTrial_ExceedRemainingDays() {
        // Given
        testUser.setTrialTotalDays(6);
        testUser.setTrialRemainingDays(2);
        when(userFrontService.getById(testUserId)).thenReturn(testUser);
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(6);

        // When & Then
        Exception exception = assertThrows(Exception.class, () -> {
            userTrialService.openTrial(testUserId, 5, "测试"); // 申请5天，但只剩2天
        });
        assertTrue(exception.getMessage().contains("试用天数不足"));
    }

    @Test
    void testGetCurrentTrialStatus_NoTrial() {
        // Given
        when(userFrontService.getById(testUserId)).thenReturn(testUser);
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(0);
        when(userTrialMapper.selectCurrentValidTrial(testUserId)).thenReturn(null);

        // When
        TrialStatusVO status = userTrialService.getCurrentTrialStatus(testUserId);

        // Then
        assertNotNull(status);
        assertEquals(testUserId, status.getUserId());
        assertEquals(0, status.getTotalUsedDays());
        assertEquals(8, status.getRemainingDays());
        assertFalse(status.getInTrial());
        assertEquals("可开通试用", status.getStatusDescription());
    }

    @Test
    void testGetCurrentTrialStatus_InTrial() {
        // Given
        InzUserTrialLog currentTrial = new InzUserTrialLog();
        currentTrial.setUserId(testUserId);
        currentTrial.setTrialDays(3);
        currentTrial.setStartDate(new Date());
        currentTrial.setEndDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000)); // 明天结束
        currentTrial.setStatus(1);

        testUser.setTrialTotalDays(3);
        testUser.setTrialRemainingDays(5);

        when(userFrontService.getById(testUserId)).thenReturn(testUser);
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(3);
        when(userTrialMapper.selectCurrentValidTrial(testUserId)).thenReturn(currentTrial);

        // When
        TrialStatusVO status = userTrialService.getCurrentTrialStatus(testUserId);

        // Then
        assertNotNull(status);
        assertEquals(testUserId, status.getUserId());
        assertEquals(3, status.getTotalUsedDays());
        assertEquals(5, status.getRemainingDays());
        assertTrue(status.getInTrial());
        assertEquals("试用中", status.getStatusDescription());
        assertNotNull(status.getTrialEndDate());
    }

    @Test
    void testCalculateRemainingTrialDays() {
        // Given
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(3);
        when(userFrontService.getById(testUserId)).thenReturn(testUser);

        // When
        Integer remainingDays = userTrialService.calculateRemainingTrialDays(testUserId);

        // Then
        assertEquals(5, remainingDays); // 8 - 3 = 5
    }

    @Test
    void testCalculateRemainingTrialDays_ExceedMax() {
        // Given
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(10); // 超过8天
        when(userFrontService.getById(testUserId)).thenReturn(testUser);

        // When
        Integer remainingDays = userTrialService.calculateRemainingTrialDays(testUserId);

        // Then
        assertEquals(0, remainingDays); // 不能为负数
    }

    @Test
    void testValidateTrialRequest_Valid() {
        // Given
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(3);
        when(userFrontService.getById(testUserId)).thenReturn(testUser);

        // When
        boolean isValid = userTrialService.validateTrialRequest(testUserId, 3);

        // Then
        assertTrue(isValid); // 已用3天，申请3天，总共6天 < 8天
    }

    @Test
    void testValidateTrialRequest_Invalid() {
        // Given
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(6);
        when(userFrontService.getById(testUserId)).thenReturn(testUser);

        // When
        boolean isValid = userTrialService.validateTrialRequest(testUserId, 5);

        // Then
        assertFalse(isValid); // 已用6天，申请5天，总共11天 > 8天
    }

    @Test
    void testValidateTrialRequest_InvalidParams() {
        // When & Then
        assertFalse(userTrialService.validateTrialRequest(null, 3));
        assertFalse(userTrialService.validateTrialRequest(testUserId, null));
        assertFalse(userTrialService.validateTrialRequest(testUserId, 0));
        assertFalse(userTrialService.validateTrialRequest(testUserId, -1));
        assertFalse(userTrialService.validateTrialRequest(testUserId, 10)); // 超过最大限制
    }

    @Test
    void testGetTotalUsedDays() {
        // Given
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(5);

        // When
        Integer totalDays = userTrialService.getTotalUsedDays(testUserId);

        // Then
        assertEquals(5, totalDays);
    }

    @Test
    void testGetTotalUsedDays_NullResult() {
        // Given
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(null);

        // When
        Integer totalDays = userTrialService.getTotalUsedDays(testUserId);

        // Then
        assertEquals(0, totalDays);
    }

    @Test
    void testIsInTrialPeriod_True() {
        // Given
        InzUserTrialLog currentTrial = new InzUserTrialLog();
        when(userTrialMapper.selectCurrentValidTrial(testUserId)).thenReturn(currentTrial);

        // When
        Boolean inTrial = userTrialService.isInTrialPeriod(testUserId);

        // Then
        assertTrue(inTrial);
    }

    @Test
    void testIsInTrialPeriod_False() {
        // Given
        when(userTrialMapper.selectCurrentValidTrial(testUserId)).thenReturn(null);

        // When
        Boolean inTrial = userTrialService.isInTrialPeriod(testUserId);

        // Then
        assertFalse(inTrial);
    }

    @Test
    void testFixTrialDataConsistency() {
        // Given
        when(userTrialMapper.sumTrialDaysByUserId(testUserId)).thenReturn(5);
        when(userFrontService.getById(testUserId)).thenReturn(testUser);
        when(userFrontService.updateById(any(UserFront.class))).thenReturn(true);

        // When
        Boolean result = userTrialService.fixTrialDataConsistency(testUserId);

        // Then
        assertTrue(result);
        verify(userFrontService).updateById(argThat(user -> 
            user.getTrialTotalDays().equals(5) && 
            user.getTrialRemainingDays().equals(3)
        ));
    }
}
