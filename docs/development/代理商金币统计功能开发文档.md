# 代理商金币统计功能开发文档

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-01
- **开发者**: Alex
- **项目名称**: 代理商金币统计功能
- **完成状态**: 已完成开发

## 2. 功能概述

### 2.1 需求描述
代理商后台需要显示金币统计信息：
- **总计金币**：代理商累计获得的所有金币
- **开户使用金币**：用于开户业务消耗的金币
- **剩余金币**：当前可用的金币余额

### 2.2 显示格式
```
总计金币 1000个；开户使用金币 300个，剩余金币 700个
```

## 3. 代码实现详情

### 3.1 新增文件

#### 3.1.1 GoldenBeanStatsVO.java
**文件路径**: `jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/user_front/vo/GoldenBeanStatsVO.java`

**功能**: 金币统计信息的数据传输对象
```java
@Data
@ApiModel(value = "GoldenBeanStatsVO", description = "代理商金币统计信息")
public class GoldenBeanStatsVO implements Serializable {
    @ApiModelProperty(value = "总计金币")
    private Integer totalGoldenBean;
    
    @ApiModelProperty(value = "开户使用金币")
    private Integer usedGoldenBean;
    
    @ApiModelProperty(value = "剩余金币")
    private Integer remainingGoldenBean;
    
    @ApiModelProperty(value = "统计时间")
    private Date statsTime;
}
```

### 3.2 修改的文件

#### 3.2.1 IInzUserFrontService.java
**修改内容**: 添加金币统计方法声明
```java
/**
 * 获取代理商金币统计信息
 * @param userId 用户ID
 * @return GoldenBeanStatsVO 金币统计信息
 */
GoldenBeanStatsVO getGoldenBeanStats(String userId);
```

#### 3.2.2 InzUserFrontServiceImpl.java
**修改内容**: 实现金币统计方法

**核心实现逻辑**:
```java
@Override
public GoldenBeanStatsVO getGoldenBeanStats(String userId) {
    // 1. 获取总计金币（所有收入记录）
    Integer totalGoldenBean = getTotalGoldenBean(userId);
    
    // 2. 获取开户使用金币（支出记录中包含开通关键字的）
    Integer usedGoldenBean = getUsedGoldenBean(userId);
    
    // 3. 获取剩余金币（当前余额）
    Integer remainingGoldenBean = getRemainingGoldenBean(userId);
    
    // 4. 组装统计结果
    GoldenBeanStatsVO stats = new GoldenBeanStatsVO();
    stats.setTotalGoldenBean(totalGoldenBean);
    stats.setUsedGoldenBean(usedGoldenBean);
    stats.setRemainingGoldenBean(remainingGoldenBean);
    stats.setStatsTime(new Date());
    
    return stats;
}
```

**辅助方法**:
1. `getTotalGoldenBean(String userId)` - 获取总计金币
2. `getUsedGoldenBean(String userId)` - 获取开户使用金币
3. `getRemainingGoldenBean(String userId)` - 获取剩余金币

#### 3.2.3 InzUserFrontController.java
**修改内容**: 添加金币统计接口

**接口路径**: `GET /user_front/inzUserFront/getGoldenBeanStats`

**权限控制**:
- 只有代理商角色可以访问
- 每个代理商只能查看自己的统计数据

## 4. 统计逻辑说明

### 4.1 总计金币计算
```sql
SELECT SUM(golden_bean) 
FROM inz_user_pay_log 
WHERE user_id = ? AND type = 1
```
- 统计该用户所有 `type=1`（收入）的金币记录
- 包括：推荐奖励、系统奖励、管理员充值等

### 4.2 开户使用金币计算
```sql
SELECT SUM(golden_bean) 
FROM inz_user_pay_log 
WHERE user_id = ? 
  AND type = 0 
  AND (content LIKE '%开通%' OR content LIKE '%开户%')
```
- 统计该用户所有 `type=0`（支出）且内容包含"开通"或"开户"的记录
- 主要是为其他用户开通课程消耗的金币

### 4.3 剩余金币获取
```sql
SELECT golden_bean 
FROM inz_user_front 
WHERE id = ?
```
- 直接从用户表获取当前的金币余额
- 这是实时的可用金币数量

## 5. 接口说明

### 5.1 请求信息
- **方法**: GET
- **路径**: `/user_front/inzUserFront/getGoldenBeanStats`
- **权限**: 需要代理商角色权限
- **参数**: 无（从登录用户信息获取）

### 5.2 返回数据结构
```json
{
  "success": true,
  "result": {
    "totalGoldenBean": 1000,
    "usedGoldenBean": 300,
    "remainingGoldenBean": 700,
    "statsTime": "2025-08-01 10:30:00"
  },
  "code": 200,
  "message": "操作成功"
}
```

### 5.3 错误响应
```json
{
  "success": false,
  "message": "您没有权限查看金币统计信息",
  "code": 500
}
```

## 6. 权限控制

### 6.1 角色权限
只有以下角色可以查看金币统计：
- `chuang` - 创总
- `channel` - 渠道
- `area_partner` - 区/县合伙人
- `city_partner` - 市级合伙人
- `province_partner` - 省级合伙人

### 6.2 数据权限
- 每个代理商只能查看自己的金币统计
- 通过手机号关联后台用户和前台用户
- 严格的权限验证，防止数据泄露

## 7. 异常处理

### 7.1 异常情况
1. **用户无权限**: 返回权限错误信息
2. **用户不存在**: 返回用户不存在错误
3. **数据查询异常**: 返回默认统计数据（全部为0）
4. **系统异常**: 记录错误日志，返回通用错误信息

### 7.2 降级策略
- 单个统计项查询失败时，该项返回0，不影响其他统计
- 数据库异常时，返回默认统计信息
- 确保接口始终有响应，不会因为异常导致前端报错

## 8. 性能优化

### 8.1 查询优化
- 使用 MyBatis Plus 的 LambdaQueryWrapper 进行类型安全查询
- 分别查询三个统计项，避免复杂的联合查询
- 添加适当的数据库索引提升查询性能

### 8.2 日志记录
- 记录关键操作的debug日志
- 记录异常情况的error日志
- 记录权限验证和统计结果的info日志

## 9. 测试建议

### 9.1 功能测试
1. **正常代理商测试**: 验证有金币记录的代理商能正确显示统计
2. **新代理商测试**: 验证无金币记录的代理商显示全部为0
3. **权限测试**: 验证非代理商用户无法访问接口
4. **数据准确性测试**: 验证统计数据与实际记录一致

### 9.2 异常测试
1. **数据库异常测试**: 模拟数据库连接异常
2. **权限异常测试**: 测试无权限用户的访问
3. **数据不一致测试**: 测试金币记录与余额不一致的情况

### 9.3 性能测试
1. **响应时间测试**: 验证接口响应时间 < 1秒
2. **并发测试**: 测试多个代理商同时查询统计
3. **大数据量测试**: 测试有大量金币记录的用户查询性能

## 10. 部署说明

### 10.1 数据库准备
确认相关表结构和数据完整性：
- `inz_user_front` 表的 `golden_bean` 字段
- `inz_user_pay_log` 表的完整记录

### 10.2 应用部署
1. 部署修改后的代码
2. 重启应用服务
3. 验证接口功能正常
4. 监控接口性能和错误日志

### 10.3 前端集成
前端需要：
1. 在代理商后台添加统计信息显示组件
2. 调用 `/user_front/inzUserFront/getGoldenBeanStats` 接口
3. 按照指定格式显示统计信息

## 11. 版本历史
- v1.0 (2025-08-01): 初始版本，Alex开发完成
