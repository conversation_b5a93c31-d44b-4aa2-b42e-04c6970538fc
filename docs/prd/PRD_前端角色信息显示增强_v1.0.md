# 前端角色信息显示增强需求文档 (PRD)

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-02
- **负责人**: Emma (产品经理)
- **项目名称**: 前端角色信息显示增强
- **优先级**: 高

## 2. 背景与问题陈述

### 2.1 当前问题
从日志可以看出，后端已经正确获取了用户的角色信息：
- 用户角色查询成功：角色代码 [chuang]，角色描述：创总
- 权限验证正常：代理商用户访问，查看推荐用户

但是前端无法获取到这些信息，需要在接口返回中包含：
1. **用户角色信息**：角色代码、角色描述
2. **金币统计信息**：总计金币、开户使用金币、剩余金币
3. **权限信息**：用户权限范围说明

### 2.2 业务需求
前端需要显示：
- 当前登录用户的角色信息
- 代理商的金币统计信息
- 根据角色显示不同的功能菜单

## 3. 目标与成功指标

### 3.1 项目目标
- 在用户列表接口中返回当前用户的角色信息
- 为代理商用户返回金币统计信息
- 提供权限范围说明，便于前端控制功能显示

### 3.2 关键结果 (Key Results)
- 前端能正确显示用户角色信息
- 代理商能看到金币统计信息
- 不同角色用户看到对应的功能界面

## 4. 功能规格详述

### 4.1 用户列表接口增强
在 `/user_front/inzUserFront/list` 接口的返回结果中增加用户信息：

```json
{
  "success": true,
  "result": {
    "records": [...], // 用户列表数据
    "total": 100,
    "size": 10,
    "current": 1,
    "userInfo": {
      "userId": "1951282902243266562",
      "username": "chaiyi",
      "phone": "15195993911",
      "roleCode": "chuang",
      "roleDescription": "创总",
      "isAgent": true,
      "isAdmin": false,
      "permissions": ["view_referrals", "manage_golden_bean"]
    },
    "goldenBeanStats": {
      "totalGoldenBean": 1000,
      "usedGoldenBean": 300,
      "remainingGoldenBean": 700,
      "statsTime": "2025-08-02 01:38:23"
    }
  }
}
```

### 4.2 数据结构说明

#### 4.2.1 userInfo 对象
- `userId`: 用户ID
- `username`: 用户名
- `phone`: 手机号
- `roleCode`: 角色代码
- `roleDescription`: 角色描述
- `isAgent`: 是否为代理商
- `isAdmin`: 是否为系统管理员
- `permissions`: 权限列表

#### 4.2.2 goldenBeanStats 对象（仅代理商返回）
- `totalGoldenBean`: 总计金币
- `usedGoldenBean`: 开户使用金币
- `remainingGoldenBean`: 剩余金币
- `statsTime`: 统计时间

### 4.3 权限控制
- 系统管理员：返回完整的用户信息和权限
- 代理商：返回角色信息和金币统计
- 普通用户：返回基础角色信息

## 5. 前端显示需求

### 5.1 角色信息显示
在页面顶部显示当前用户信息：
```
当前用户：chaiyi (创总) | 手机号：15195993911
```

### 5.2 金币统计显示（仅代理商）
在用户列表页面顶部显示金币统计卡片：
```
┌─────────────────────────────────────────────────────────┐
│  金币统计                                                │
│  总计金币：1000个 | 开户使用金币：300个 | 剩余金币：700个   │
└─────────────────────────────────────────────────────────┘
```

### 5.3 功能菜单控制
根据角色显示不同的功能：
- **系统管理员**：显示所有功能菜单
- **代理商**：显示推荐用户管理、金币统计等
- **普通用户**：显示基础功能

## 6. 技术实现要点

### 6.1 后端修改
1. 修改用户列表接口，在返回结果中添加用户信息
2. 为代理商用户添加金币统计信息
3. 添加权限信息的组装逻辑

### 6.2 前端修改
1. 解析接口返回的用户信息
2. 根据角色信息控制界面显示
3. 显示金币统计信息（如果是代理商）

## 7. 边缘情况处理
- 角色信息获取失败：显示默认角色信息
- 金币统计获取失败：显示默认统计（全部为0）
- 权限信息缺失：按最小权限处理

## 8. 版本历史
- v1.0 (2025-08-02): 初始版本，Emma创建
