# 用户角色信息增强需求文档 (PRD)

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-01
- **负责人**: Emma (产品经理)
- **项目名称**: 用户角色信息增强
- **优先级**: 高

## 2. 背景与问题陈述

### 2.1 当前问题
用户在登录系统后，通过 `/sys/user/getUserInfo` 接口获取的用户信息中，角色字段（role）为空，导致前端无法正确显示用户的角色信息。

### 2.2 问题影响
- 前端界面无法显示用户角色，影响用户体验
- 权限管理界面缺少角色信息展示
- 用户无法清楚了解自己的权限范围
- 管理员无法通过界面快速识别用户角色

### 2.3 根本原因
1. 当前 `getUserInfo` 接口只返回基础用户信息，未包含角色详情
2. 系统虽有完整的角色管理体系，但未在用户信息接口中关联返回
3. 前端期望的角色信息结构与后端返回结构不匹配

## 3. 目标与成功指标

### 3.1 项目目标
- **主要目标**: 在用户信息接口中增加完整的角色信息返回
- **次要目标**: 优化角色信息的数据结构，便于前端使用

### 3.2 关键结果 (Key Results)
- 用户登录后能正确显示角色信息（角色名称、角色代码、角色描述）
- 角色列表接口返回完整的角色详情
- 前端界面能正确展示用户的多角色信息

### 3.3 成功指标
- 用户信息接口响应时间 < 500ms
- 角色信息准确率 100%
- 前端角色显示正常率 100%

## 4. 用户画像与用户故事

### 4.1 目标用户
- **系统管理员**: 需要查看和管理用户角色
- **普通用户**: 需要了解自己的角色和权限
- **代理商用户**: 需要确认自己的代理商身份和权限

### 4.2 用户故事
- 作为系统管理员，我希望能在用户列表中看到每个用户的角色信息，以便进行权限管理
- 作为普通用户，我希望能在个人信息页面看到我的角色，了解我的权限范围
- 作为代理商，我希望能确认我的代理商身份，以便使用相应的功能

## 5. 功能规格详述

### 5.1 核心功能
1. **用户信息接口增强**
   - 在 `/sys/user/getUserInfo` 接口中增加角色信息返回
   - 返回用户的所有角色列表，包含角色ID、角色名称、角色代码、角色描述

2. **角色信息结构优化**
   - 设计合理的角色信息数据结构
   - 支持多角色用户的信息展示

3. **兼容性保证**
   - 确保现有功能不受影响
   - 保持接口向后兼容

### 5.2 技术实现要点
1. **数据库查询优化**
   - 通过用户ID关联查询角色信息
   - 使用 LEFT JOIN 确保无角色用户也能正常返回

2. **返回数据结构**
   ```json
   {
     "userInfo": {
       "id": "用户ID",
       "username": "用户名",
       "realname": "真实姓名",
       "role": "主要角色代码",
       "roleList": [
         {
           "roleId": "角色ID",
           "roleName": "角色名称", 
           "roleCode": "角色代码",
           "description": "角色描述"
         }
       ]
     }
   }
   ```

### 5.3 边缘情况处理
- 用户无角色时，roleList 返回空数组，role 返回空字符串
- 用户有多个角色时，role 返回第一个角色的代码
- 角色信息查询异常时，记录日志但不影响用户基础信息返回

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 修改 `getUserInfo` 接口，增加角色信息返回
- 优化角色信息查询逻辑
- 更新相关的服务类和实体类
- 添加角色信息的单元测试

### 6.2 排除功能 (Out of Scope)
- 角色权限管理功能的修改
- 前端界面的修改（由前端团队负责）
- 角色分配和删除功能的修改

## 7. 依赖与风险

### 7.1 内部依赖
- 需要 ISysUserService 服务类的配合
- 需要 ISysRoleService 和 ISysUserRoleService 的支持
- 依赖现有的数据库表结构（sys_user、sys_role、sys_user_role）

### 7.2 外部依赖
- 前端团队需要相应调整显示逻辑
- 测试团队需要验证角色信息的正确性

### 7.3 潜在风险
- **性能风险**: 增加角色查询可能影响接口响应时间
- **数据一致性风险**: 角色信息与权限信息可能不同步
- **兼容性风险**: 新的数据结构可能影响现有前端代码

### 7.4 风险缓解措施
- 使用数据库查询优化，减少性能影响
- 增加数据校验逻辑，确保数据一致性
- 保持向后兼容，逐步迁移前端代码

## 8. 发布初步计划

### 8.1 开发阶段
1. **Phase 1**: 后端接口开发（2天）
   - 修改 LoginController 的 getUserInfo 方法
   - 增加角色信息查询逻辑
   - 更新相关服务类

2. **Phase 2**: 测试验证（1天）
   - 单元测试编写和执行
   - 接口测试验证
   - 性能测试

3. **Phase 3**: 部署上线（0.5天）
   - 代码部署
   - 功能验证
   - 监控观察

### 8.2 验收标准
- 用户信息接口能正确返回角色信息
- 所有单元测试通过
- 接口响应时间符合要求
- 前端能正确解析和显示角色信息

## 9. 版本历史
- v1.0 (2025-08-01): 初始版本，Emma创建
