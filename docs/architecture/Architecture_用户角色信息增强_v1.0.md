# 用户角色信息增强技术架构设计

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-01
- **架构师**: Bob
- **项目名称**: 用户角色信息增强
- **技术栈**: Spring Boot + MyBatis Plus + MySQL

## 2. 架构概述

### 2.1 设计目标
- 在现有用户信息接口中增加角色信息返回
- 保持系统性能和稳定性
- 确保数据一致性和准确性
- 保持向后兼容性

### 2.2 核心原则
- **最小侵入**: 尽量减少对现有代码的修改
- **性能优先**: 确保接口响应时间不受显著影响
- **数据准确**: 确保角色信息的准确性和实时性
- **可扩展性**: 为未来的角色功能扩展预留空间

## 3. 系统架构设计

### 3.1 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API       │    │   数据库层      │
│                 │    │                 │    │                 │
│ - 用户信息展示  │◄──►│ LoginController │◄──►│ sys_user        │
│ - 角色信息显示  │    │ getUserInfo()   │    │ sys_role        │
│                 │    │                 │    │ sys_user_role   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 数据流设计
1. **请求流程**:
   ```
   前端请求 → LoginController.getUserInfo() → SysUserService.getUserByName()
   → 新增: SysUserService.getUserRoles() → 数据库查询 → 返回结果
   ```

2. **数据查询流程**:
   ```
   用户基础信息查询 → 角色关联查询 → 角色详情查询 → 数据组装 → 返回前端
   ```

## 4. 数据库设计

### 4.1 相关表结构
```sql
-- 用户表 (已存在)
sys_user: id, username, realname, phone, ...

-- 角色表 (已存在)  
sys_role: id, role_name, role_code, description, ...

-- 用户角色关联表 (已存在)
sys_user_role: id, user_id, role_id, ...
```

### 4.2 查询SQL设计
```sql
-- 获取用户角色信息的SQL
SELECT 
    sr.id as role_id,
    sr.role_name,
    sr.role_code, 
    sr.description
FROM sys_user_role sur
LEFT JOIN sys_role sr ON sur.role_id = sr.id  
WHERE sur.user_id = ?
ORDER BY sr.create_time ASC
```

## 5. 核心组件设计

### 5.1 Controller层修改
**文件**: `LoginController.java`
**方法**: `getUserInfo(HttpServletRequest request)`

**修改点**:
```java
// 原有逻辑保持不变
SysUser sysUser = sysUserService.getUserByName(username);

// 新增: 获取用户角色信息
List<SysRole> userRoles = sysUserService.getUserRoles(sysUser.getId());
sysUser.setRoleList(userRoles);

// 设置主要角色代码 (兼容现有逻辑)
if (!userRoles.isEmpty()) {
    sysUser.setRole(userRoles.get(0).getRoleCode());
}
```

### 5.2 Service层增强
**文件**: `ISysUserService.java` 和 `SysUserServiceImpl.java`

**新增方法**:
```java
/**
 * 获取用户的所有角色信息
 * @param userId 用户ID
 * @return 角色列表
 */
List<SysRole> getUserRoles(String userId);
```

**实现逻辑**:
```java
@Override
public List<SysRole> getUserRoles(String userId) {
    // 通过用户ID查询角色信息
    LambdaQueryWrapper<SysUserRole> userRoleQuery = new LambdaQueryWrapper<>();
    userRoleQuery.eq(SysUserRole::getUserId, userId);
    List<SysUserRole> userRoles = sysUserRoleService.list(userRoleQuery);
    
    if (userRoles.isEmpty()) {
        return new ArrayList<>();
    }
    
    // 获取角色ID列表
    List<String> roleIds = userRoles.stream()
        .map(SysUserRole::getRoleId)
        .collect(Collectors.toList());
    
    // 查询角色详情
    LambdaQueryWrapper<SysRole> roleQuery = new LambdaQueryWrapper<>();
    roleQuery.in(SysRole::getId, roleIds);
    return sysRoleService.list(roleQuery);
}
```

### 5.3 实体类增强
**文件**: `SysUser.java`

**新增字段**:
```java
/**
 * 用户角色列表 (不持久化到数据库)
 */
@TableField(exist = false)
private List<SysRole> roleList;
```

## 6. 性能优化设计

### 6.1 查询优化
- 使用 `LEFT JOIN` 减少数据库查询次数
- 添加必要的数据库索引
- 使用 MyBatis Plus 的批量查询功能

### 6.2 缓存策略
- 考虑对用户角色信息进行缓存（可选）
- 使用 Redis 缓存热点用户的角色信息
- 设置合理的缓存过期时间

### 6.3 性能监控
- 添加接口响应时间监控
- 监控数据库查询性能
- 设置性能告警阈值

## 7. 安全设计

### 7.1 数据安全
- 确保只返回当前用户有权限查看的角色信息
- 对敏感角色信息进行脱敏处理
- 添加访问日志记录

### 7.2 权限控制
- 保持现有的权限验证逻辑不变
- 确保角色信息不会泄露给未授权用户
- 添加角色信息访问的审计日志

## 8. 错误处理设计

### 8.1 异常处理策略
```java
try {
    List<SysRole> userRoles = sysUserService.getUserRoles(sysUser.getId());
    sysUser.setRoleList(userRoles);
} catch (Exception e) {
    log.error("获取用户角色信息失败, userId: {}", sysUser.getId(), e);
    // 设置空角色列表，不影响用户基础信息返回
    sysUser.setRoleList(new ArrayList<>());
}
```

### 8.2 降级策略
- 角色信息查询失败时，返回空角色列表
- 保证用户基础信息正常返回
- 记录详细的错误日志便于排查

## 9. 测试策略

### 9.1 单元测试
- 测试 `getUserRoles` 方法的各种场景
- 测试用户无角色的情况
- 测试用户多角色的情况
- 测试异常情况的处理

### 9.2 集成测试
- 测试完整的用户信息获取流程
- 测试接口响应时间
- 测试并发访问情况

### 9.3 性能测试
- 压力测试接口响应时间
- 测试大量用户同时访问的情况
- 验证数据库查询性能

## 10. 部署方案

### 10.1 部署步骤
1. **数据库准备**: 确认相关表结构和索引
2. **代码部署**: 部署修改后的代码
3. **功能验证**: 验证角色信息返回正确
4. **性能监控**: 监控接口性能指标
5. **回滚准备**: 准备快速回滚方案

### 10.2 监控指标
- 接口响应时间 < 500ms
- 接口成功率 > 99.9%
- 数据库查询时间 < 100ms
- 角色信息准确率 100%

## 11. 风险评估与缓解

### 11.1 技术风险
- **风险**: 数据库查询性能影响
- **缓解**: 添加索引，优化查询SQL，使用缓存

- **风险**: 角色信息不一致
- **缓解**: 使用事务保证数据一致性，添加数据校验

### 11.2 业务风险
- **风险**: 影响现有功能
- **缓解**: 保持向后兼容，充分测试

- **风险**: 用户体验下降
- **缓解**: 确保接口性能，添加降级策略

## 12. 版本历史
- v1.0 (2025-08-01): 初始架构设计，Bob创建
