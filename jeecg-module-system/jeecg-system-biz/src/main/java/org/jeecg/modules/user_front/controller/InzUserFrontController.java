package org.jeecg.modules.user_front.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.ThirdRequestUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.books.entity.InzWordBooks;
import org.jeecg.modules.books.service.IInzWordBooksService;
import org.jeecg.modules.education.entity.InzEducation;
import org.jeecg.modules.education.service.IInzEducationService;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.service.ISysRoleService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.user_books.entity.InzUserBooks;
import org.jeecg.modules.user_books.service.IInzUserBooksService;
import org.jeecg.modules.user_front.entity.*;
import org.jeecg.modules.user_front.service.IInzUserDeviceService;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.jeecg.modules.user_front.service.IInzUserPayLogService;
import org.jeecg.modules.user_front.vo.AgentReferralVO;
import org.jeecg.modules.user_front.vo.CurrentUserInfoVO;
import org.jeecg.modules.user_front.vo.GoldenBeanStatsVO;
import org.jeecg.modules.user_front.vo.InzUserFrontPage;
import org.jeecg.modules.user_front.vo.UserListResponseVO;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date: 2025-06-17
 * @Version: V1.0
 */
@Api(tags = "用户表")
@RestController
@RequestMapping("/user_front/inzUserFront")
@Slf4j
public class InzUserFrontController {
    @Autowired
    private IInzUserFrontService inzUserFrontService;
    @Autowired
    private IInzUserDeviceService inzUserDeviceService;
    @Autowired
    private IInzUserPayLogService inzUserPayLogService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysUserRoleService sysUserRoleService;
    @Autowired
    private BaseCommonService baseCommonService;
    @Autowired
    private IInzEducationService inzEducationService;
    @Autowired
    private IInzWordBooksService inzWordBooksService;
    @Autowired
    private IInzUserBooksService inzUserBooksService;

    /**
     * 分页列表查询
     *
     * @param inzUserFront
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "用户表-分页列表查询", notes = "用户表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<UserListResponseVO> queryPageList(InzUserFront inzUserFront,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        QueryWrapper<InzUserFront> queryWrapper = QueryGenerator.initQueryWrapper(inzUserFront, req.getParameterMap());
        Page<InzUserFront> page = new Page<InzUserFront>(pageNo, pageSize);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        log.info("用户查询列表 - 用户ID: {}, 用户名: {}", loginUser.getId(), loginUser.getUsername());

        try {
            // 1. 根据登录用户信息查找sys_user
            SysUser sysUser = sysUserService.getById(loginUser.getId());
            if (sysUser == null) {
                return Result.error("用户信息不存在");
            }

            // 2. 通过手机号获取用户角色信息（按照正确的权限链查询）
            List<String> roleCodes = getUserRoleCodesByPhone(sysUser.getPhone());

            if (roleCodes.isEmpty()) {
                log.warn("用户没有角色权限 - 用户: {}, 手机号: {}", sysUser.getUsername(), sysUser.getPhone());
                return Result.error("您没有相应的角色权限，请联系管理员");
            }

            String roleDescription = getUserRoleDescriptionByRoleCodes(roleCodes);
            log.info("用户查询列表 - 用户: {}, 手机号: {}, 角色: {}, 角色代码: {}",
                    sysUser.getUsername(), sysUser.getPhone(), roleDescription, roleCodes);

            // 3. 根据角色决定数据访问权限
            if (isSystemAdminByRoleCodes(roleCodes)) {
                // 系统管理员：查看所有用户，不添加任何过滤条件
                log.info("系统管理员访问，查看所有用户 - 角色代码: {}", roleCodes);
            } else if (isAgentUserByRoleCodes(roleCodes)) {
                // 代理商用户：只查看推荐用户
                log.info("代理商用户访问，查看推荐用户");

                // 根据用户名（通常是手机号）查找前台代理商账户
                InzUserFront currentAgent = inzUserFrontService.getOne(
                        new QueryWrapper<InzUserFront>().lambda().eq(InzUserFront::getPhone, sysUser.getPhone())
                );

                if (currentAgent == null) {
                    return Result.error("未找到对应的代理商账户");
                }

                // 获取代理商推荐的所有用户ID
                List<String> referralUserIds = getReferralUserIds(currentAgent.getId());

                if (referralUserIds.isEmpty()) {
                    // 没有推荐用户，返回空结果
                    return Result.OK(new Page<>(pageNo, pageSize, 0));
                }

                // 限制查询范围为推荐用户
                queryWrapper.lambda().in(InzUserFront::getId, referralUserIds);
            } else {
                // 其他角色：无权限
                return Result.error("您没有权限查看用户列表");
            }

        } catch (Exception e) {
            log.error("查询用户列表失败 - 用户: {}", loginUser.getUsername(), e);
            return Result.error("查询失败: " + e.getMessage());
        }

        IPage<InzUserFront> pageList = inzUserFrontService.page(page, queryWrapper);

        // 创建增强的返回对象
        UserListResponseVO response = UserListResponseVO.fromIPage(pageList);

        // 添加当前用户信息
        try {
            CurrentUserInfoVO currentUserInfo = buildCurrentUserInfo(sysUser, roleCodes);
            response.setUserInfo(currentUserInfo);

            // 如果是代理商，添加金币统计信息
            if (isAgentUserByRoleCodes(roleCodes)) {
                InzUserFront currentAgent = inzUserFrontService.getOne(
                        new QueryWrapper<InzUserFront>().lambda().eq(InzUserFront::getPhone, sysUser.getPhone())
                );

                if (currentAgent != null) {
                    GoldenBeanStatsVO goldenBeanStats = inzUserFrontService.getGoldenBeanStats(currentAgent.getId());
                    response.setGoldenBeanStats(goldenBeanStats);

                    log.info("返回代理商金币统计 - 用户: {}, 总计: {}, 使用: {}, 剩余: {}",
                            sysUser.getUsername(), goldenBeanStats.getTotalGoldenBean(),
                            goldenBeanStats.getUsedGoldenBean(), goldenBeanStats.getRemainingGoldenBean());
                }
            }

        } catch (Exception e) {
            log.error("构建用户信息失败 - 用户: {}", sysUser.getUsername(), e);
            // 设置默认用户信息，不影响主要功能
            response.setUserInfo(CurrentUserInfoVO.createDefault(sysUser.getUsername()));
        }

        return Result.OK(response);
    }

    // 递归查找方法
    private void findSubordinates(String userId,
                                  Map<String, List<String>> userSubordinatesMap,
                                  List<String> result) {
        List<String> directSubordinates = userSubordinatesMap.getOrDefault(userId, Collections.emptyList());
        for (String subId : directSubordinates) {
            result.add(subId);
            findSubordinates(subId, userSubordinatesMap, result); // 递归
        }
    }

    @ApiOperation(value = "用户选择图书管理-分页列表查询", notes = "用户选择图书管理-分页列表查询")
    @GetMapping(value = "/listAll")
    public Result<List<InzUserFront>> listAll(InzUserFront inzUserFront,
                                              HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> allSubordinateIds = new ArrayList<>();
        if ("test".equals(sysUser.getRoleCode())) {

            InzUserFront userFront = inzUserFrontService.getOne(new QueryWrapper<InzUserFront>().lambda().eq(InzUserFront::getPhone, sysUser.getUsername()));
            allSubordinateIds.add(userFront.getId());
            // 1. 先查询所有用户数据（可根据业务需要加.where条件）
            List<InzUserFront> allUsers = inzUserFrontService.list();

            // 2. 构建用户ID -> 下级ID列表的映射（自动跳过parentId=null的记录）
            Map<String, List<String>> userSubordinatesMap = allUsers.stream()
                    .filter(user -> user.getParentId() != null && !user.getParentId().isEmpty())
                    .collect(Collectors.groupingBy(
                            InzUserFront::getParentId,
                            Collectors.mapping(InzUserFront::getId, Collectors.toList())
                    ));

            // 3. 如果当前用户没有下级，直接返回空
            if (!userSubordinatesMap.containsKey(userFront.getId())) {
                return Result.OK(null);
            }

            // 4. 递归查找所有下级ID

            findSubordinates(userFront.getId(), userSubordinatesMap, allSubordinateIds);
        }
        List<InzUserFront> pageList = inzUserFrontService.list(new QueryWrapper<InzUserFront>().lambda().in(!allSubordinateIds.isEmpty(), InzUserFront::getId, allSubordinateIds));
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param inzUserFrontPage
     * @return
     */
    @AutoLog(value = "用户表-添加")
    @ApiOperation(value = "用户表-添加", notes = "用户表-添加")
    @RequiresPermissions("user_front:inz_user_front:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzUserFrontPage inzUserFrontPage) {
        InzUserFront inzUserFront = new InzUserFront();
        BeanUtils.copyProperties(inzUserFrontPage, inzUserFront);
        inzUserFrontService.saveMain(inzUserFront, inzUserFrontPage.getInzUserDeviceList(), inzUserFrontPage.getInzUserPayLogList());
        return Result.OK("添加成功！");
    }

    /**
     * 编辑用户信息
     *
     * @param inzUserFrontPage 用户前端页面对象
     * @return 操作结果
     */
    @AutoLog(value = "用户表-编辑")
    @ApiOperation(value = "用户表-编辑", notes = "用户表-编辑")
    @RequiresPermissions("user_front:inz_user_front:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzUserFrontPage inzUserFrontPage) throws Exception {
        // 参数校验
        if (inzUserFrontPage == null || inzUserFrontPage.getId() == null) {
            return Result.error("参数错误");
        }

        // 转换DTO到Entity
        InzUserFront inzUserFront = new InzUserFront();
        BeanUtils.copyProperties(inzUserFrontPage, inzUserFront);

        // 处理VIP时间
        if (StringUtils.isNotBlank(inzUserFrontPage.getVipTime())) {
            try {
                // 先尝试去除时间部分，只保留日期部分
                String datePart = inzUserFrontPage.getVipTime().trim().split(" ")[0];
                LocalDate vipDate = LocalDate.parse(datePart);
                LocalDateTime vipDateTime = vipDate.atStartOfDay();
                inzUserFront.setVipTime(Date.from(vipDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            } catch (DateTimeParseException e) {
                log.error("日期格式转换失败 | 原始值: {} | 错误: {}", inzUserFrontPage.getVipTime(), e.getMessage());
                return Result.error("会员时间格式错误，请使用 yyyy-MM-dd 格式");
            } catch (Exception e) {
                log.error("处理VIP时间发生意外错误 | 原始值: {} | 错误: {}", inzUserFrontPage.getVipTime(), e.getMessage());
                return Result.error("处理会员时间时发生错误");
            }
        }

        // 验证数据存在性
        InzUserFront existingUser = inzUserFrontService.getById(inzUserFront.getId());
        if (existingUser == null) {
            return Result.error("未找到对应数据");
        }

        SysUser userByName = sysUserService.getUserByName(inzUserFront.getPhone());
        if ("chuang".equals(inzUserFront.getRole()) || "channel".equals(inzUserFront.getRole()) || "area_partner".equals(inzUserFront.getRole()) || "city_partner".equals(inzUserFront.getRole()) || "province".equals(inzUserFront.getRole())) {
            if (userByName != null) {
                if (userByName.getStatus() != 1) {
                    sysUserService.updateStatus(userByName.getId(), "1");
                }
            } else {
                SysUser sysUser = new SysUser();
                sysUser.setUsername(existingUser.getPhone());
                sysUser.setRealname(existingUser.getRealName());
                sysUser.setPassword(existingUser.getPassword());
                sysUser.setSalt(existingUser.getSalt());
                sysUser.setStatus(1);
                sysUser.setDelFlag(CommonConstant.DEL_FLAG_0);
                sysUser.setOrgCode(null);
                sysUser.setUserIdentity(1);
                sysUserService.saveUser(sysUser, "ee8626f80f7c2619917b6236f3a7f02b", null, null);
                baseCommonService.addLog("添加用户，username： " + sysUser.getUsername(), CommonConstant.LOG_TYPE_2, 2);
            }
        } else {
            if (userByName != null) {
                sysUserService.updateStatus(userByName.getId(), "0");
            }
        }

        // 处理用户奖励逻辑
        processUserReward(existingUser, inzUserFront);

        // 更新数据
        inzUserFrontService.updateMain(inzUserFront, inzUserFrontPage.getInzUserDeviceList(), inzUserFrontPage.getInzUserPayLogList());
        return Result.OK("编辑成功!");
    }

    /**
     * 处理用户奖励逻辑
     *
     * @param existingUser 数据库中已存在的用户实体
     * @param updatedUser  更新后的用户实体
     */
    private void processUserReward(InzUserFront existingUser, InzUserFront updatedUser) throws Exception {
        // 1. 前置条件检查
        if (StringUtils.isBlank(existingUser.getParentId())) {
            return;
        }

        // 2. 获取直接上级用户
        InzUserFront parentUser = inzUserFrontService.getById(existingUser.getParentId());
        if (parentUser == null) {
            return;
        }

        // 3. 获取角色类型（带空值检查）
        Integer updatedUserRoleType = getSafeRoleType(updatedUser.getRole());
        Integer parentUserRoleType = getSafeRoleType(parentUser.getRole());

        // 如果更新用户或上级用户的角色类型为空，则不处理奖励
        if (updatedUserRoleType == null || parentUserRoleType == null) {
            return;
        }

        // 4. 获取间接上级用户（如果有）
        InzUserFront indirectUser = null;
        Integer indirectUserRoleType = null;
        if (StringUtils.isNotBlank(parentUser.getParentId())) {
            indirectUser = inzUserFrontService.getById(parentUser.getParentId());
            if (indirectUser != null) {
                indirectUserRoleType = getSafeRoleType(indirectUser.getRole());
            }
        }

        // 5. 计算奖励
        Map<String, String> userBeanReward = ThirdRequestUtils.getUserBeanReward(
                updatedUserRoleType,
                parentUserRoleType,
                indirectUserRoleType,
                null
        );

        if (MapUtils.isEmpty(userBeanReward)) {
            return;
        }

        // 6. 处理直接奖励
        if (StringUtils.isNotBlank(userBeanReward.get("directReward"))) {
            processReward(
                    parentUser,
                    userBeanReward.get("directReward"),
                    "直推VIP奖励",
                    updatedUser.getId(),
                    "direct"
            );
        }

        // 7. 处理间接奖励（如果存在间接用户且返回了间接奖励）
        if (indirectUser != null && StringUtils.isNotBlank(userBeanReward.get("indirectReward"))) {
            processReward(
                    indirectUser,
                    userBeanReward.get("indirectReward"),
                    "间推VIP奖励",
                    updatedUser.getId(),
                    "indirect"
            );
        }
    }

    /**
     * 处理奖励逻辑（完整版）
     */
    private void processReward(InzUserFront rewardUser, String rewardStr,
                               String rewardContent, String sourceUserId, String rewardType) {
        try {
            int reward = Integer.parseInt(rewardStr);

            // 1. 更新用户金豆
            rewardUser.setGoldenBean(rewardUser.getGoldenBean() + reward);
            inzUserFrontService.updateById(rewardUser);

            // 2. 创建奖励记录
            InzUserPayLog rewardLog = new InzUserPayLog();
            rewardLog.setUserId(rewardUser.getId());
            rewardLog.setSourceUserId(sourceUserId);
            rewardLog.setType(1); // 1表示收入
            rewardLog.setGoldenBean(reward);
            rewardLog.setContent(rewardContent);
            rewardLog.setCreateTime(new Date());

            inzUserPayLogService.save(rewardLog);

            log.info("成功发放{}给用户{}，奖励金额: {}，来源用户: {}",
                    rewardContent, rewardUser.getId(), reward, sourceUserId);
        } catch (NumberFormatException e) {
            log.error("奖励金额格式错误: {}", rewardStr, e);
        }
    }

    @AutoLog(value = "开通单词书")
    @ApiOperation(value = "开通单词书", notes = "开通单词书")
    @PostMapping(value = "/openBook")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> openBook(@RequestBody openRequest openBook) {
        // 1. 获取用户和代理商信息
        InzUserFront user = inzUserFrontService.getOne(new QueryWrapper<>(InzUserFront.class).lambda().eq(InzUserFront::getRealName, openBook.getUserName()));
        InzUserFront agent = inzUserFrontService.getOne(new QueryWrapper<>(InzUserFront.class).lambda().eq(InzUserFront::getRealName, openBook.getRuserName()));

        if (user == null) {
            return Result.error("用户不存在，请检查用户名");
        }

        if (agent == null) {
            return Result.error("代理商不存在，请检查代理商用户名");
        }

        // 2. 验证代理商身份
        String agentRole = agent.getRole();
        if (!"test".equals(agentRole) && !"channel".equals(agentRole) && !"city_partner".equals(agentRole) &&
                !"province".equals(agentRole) && !"area_partner".equals(agentRole) && !"chuang".equals(agentRole)) {
            return Result.error("没有开通课程的权限，仅代理商可以开通课程");
        }

        // 3. 验证用户身份
        if (!(user.getRole().equals("vip") || user.getRole().equals("chuang") ||
                user.getRole().equals("normal") || user.getRole().equals("trial"))) {
            return Result.error("该用户无法开通课程权限");
        }

        // 4. 获取教育阶段信息
        QueryWrapper<InzEducation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(InzEducation::getName, openBook.getEducationName());
        InzEducation education = inzEducationService.getOne(queryWrapper);
        if (education == null) {
            return Result.error("未找到对应的教育阶段，请检查教育阶段名称");
        }

        // 5. 获取该阶段下的所有单词书
        QueryWrapper<InzWordBooks> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().eq(InzWordBooks::getEducationId, education.getId());
        List<InzWordBooks> wordBooks = inzWordBooksService.list(queryWrapper1);
        if (wordBooks == null || wordBooks.isEmpty()) {
            return Result.error("该教育阶段下没有可用的单词书");
        }

        // 6. 验证代理商金币是否足够
        if (agent.getGoldenBean() < education.getGoldenBean()) {
            return Result.error("代理商金币不足，无法开通课程，需要" + education.getGoldenBean() + "金币");
        }

        // 7. 扣除代理商金币
        agent.setGoldenBean(agent.getGoldenBean() - education.getGoldenBean());
        boolean updateResult = inzUserFrontService.updateById(agent);
        if (!updateResult) {
            return Result.error("扣除金币失败，请重试");
        }

        // 8. 记录金币消费日志
        InzUserPayLog payLog = new InzUserPayLog();
        payLog.setUserId(agent.getId());
        payLog.setType(0); // 0表示支出
        payLog.setGoldenBean(education.getGoldenBean());
        payLog.setContent("为用户[" + user.getRealName() + "]开通[" + education.getName() + "]阶段课程");
        payLog.setSourceUserId(user.getId());
        payLog.setCreateTime(new Date());
        inzUserPayLogService.save(payLog);

        // 9. 开通用户单词书权限
        int successCount = 0;
        for (InzWordBooks wordBook : wordBooks) {
            // 检查用户是否已经有该单词书权限
            QueryWrapper<InzUserBooks> checkExist = new QueryWrapper<>();
            checkExist.lambda()
                    .eq(InzUserBooks::getWordBookId, wordBook.getId())
                    .eq(InzUserBooks::getUpdateBy, user.getId());
            Long count = inzUserBooksService.count(checkExist);

            if (count == 0) { // 如果用户没有该单词书权限，则添加
                InzUserBooks inzUserBooks = new InzUserBooks();
                inzUserBooks.setWordBookId(wordBook.getId());
                inzUserBooks.setUpdateBy(user.getId());
                inzUserBooks.setCreateBy(agent.getId());

                // 设置一年的有效期
                Calendar calendar = Calendar.getInstance();
                // 当前日期作为开始时间
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String startDate = dateFormat.format(new Date());
                inzUserBooks.setExpirationStartData(startDate);

                // 一年后的日期作为结束时间
                calendar.add(Calendar.YEAR, 1);
                String endDate = dateFormat.format(calendar.getTime());
                inzUserBooks.setExpirationEndData(endDate);

                // 设置状态为使用中
                inzUserBooks.setStatus(1);

                boolean saveResult = inzUserBooksService.save(inzUserBooks);
                if (saveResult) {
                    successCount++;
                }
            }
        }

        if (successCount > 0) {
            return Result.OK("成功为用户[" + user.getRealName() + "]开通[" + education.getName() + "]阶段，共" + successCount + "本单词书，扣除" + education.getGoldenBean() + "金币");
        } else {
            // 如果没有成功添加任何单词书，回滚事务
            throw new RuntimeException("没有成功添加任何单词书权限");
        }
    }

    /**
     * 安全获取角色类型
     */
    private Integer getSafeRoleType(String role) {
        try {
            return CommonUtils.getRoleType(role);
        } catch (Exception e) {
            log.error("获取角色类型失败，角色值: {}", role, e);
            return null;
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "用户表-通过id删除")
    @ApiOperation(value = "用户表-通过id删除", notes = "用户表-通过id删除")
    @RequiresPermissions("user_front:inz_user_front:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzUserFrontService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "用户表-批量删除")
    @ApiOperation(value = "用户表-批量删除", notes = "用户表-批量删除")
    @RequiresPermissions("user_front:inz_user_front:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzUserFrontService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "用户表-通过id查询")
    @ApiOperation(value = "用户表-通过id查询", notes = "用户表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzUserFront> queryById(@RequestParam(name = "id", required = true) String id) {
        InzUserFront inzUserFront = inzUserFrontService.getById(id);
        if (inzUserFront == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzUserFront);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "用户登录设备-通过主表ID查询")
    @ApiOperation(value = "用户登录设备-通过主表ID查询", notes = "用户登录设备-通过主表ID查询")
    @GetMapping(value = "/queryInzUserDeviceByMainId")
    public Result<IPage<InzUserDevice>> queryInzUserDeviceListByMainId
    (@RequestParam(name = "id", required = true) String id) {
        List<InzUserDevice> inzUserDeviceList = inzUserDeviceService.selectByMainId(id);
        IPage<InzUserDevice> page = new Page<>();
        page.setRecords(inzUserDeviceList);
        page.setTotal(inzUserDeviceList.size());
        return Result.OK(page);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "用户金豆记录-通过主表ID查询")
    @ApiOperation(value = "用户金豆记录-通过主表ID查询", notes = "用户金豆记录-通过主表ID查询")
    @GetMapping(value = "/queryInzUserPayLogByMainId")
    public Result<IPage<InzUserPayLog>> queryInzUserPayLogListByMainId
    (@RequestParam(name = "id", required = true) String id) {
        List<InzUserPayLog> inzUserPayLogList = inzUserPayLogService.selectByMainId(id);
        IPage<InzUserPayLog> page = new Page<>();
        page.setRecords(inzUserPayLogList);
        page.setTotal(inzUserPayLogList.size());
        return Result.OK(page);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzUserFront
     */
    @RequiresPermissions("user_front:inz_user_front:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzUserFront inzUserFront) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<InzUserFront> queryWrapper = QueryGenerator.initQueryWrapper(inzUserFront, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //配置选中数据查询条件
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id", selectionList);
        }
        //Step.2 获取导出数据
        List<InzUserFront> inzUserFrontList = inzUserFrontService.list(queryWrapper);

        // Step.3 组装pageList
        List<InzUserFrontPage> pageList = new ArrayList<InzUserFrontPage>();
        for (InzUserFront main : inzUserFrontList) {
            InzUserFrontPage vo = new InzUserFrontPage();
            BeanUtils.copyProperties(main, vo);
            List<InzUserDevice> inzUserDeviceList = inzUserDeviceService.selectByMainId(main.getId());
            vo.setInzUserDeviceList(inzUserDeviceList);
            List<InzUserPayLog> inzUserPayLogList = inzUserPayLogService.selectByMainId(main.getId());
            vo.setInzUserPayLogList(inzUserPayLogList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "用户表列表");
        mv.addObject(NormalExcelConstants.CLASS, InzUserFrontPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("用户表数据", "导出人:" + sysUser.getRealname(), "用户表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("user_front:inz_user_front:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<InzUserFrontPage> list = ExcelImportUtil.importExcel(file.getInputStream(), InzUserFrontPage.class, params);
                for (InzUserFrontPage page : list) {
                    InzUserFront po = new InzUserFront();
                    BeanUtils.copyProperties(page, po);
                    inzUserFrontService.saveMain(po, page.getInzUserDeviceList(), page.getInzUserPayLogList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

    /**
     * 金豆调整
     */
    @AutoLog(value = "用户表-金豆调整")
    @ApiOperation(value = "用户表-金豆调整", notes = "用户表-金豆调整")
    @PostMapping(value = "/assignBeans")
    @Transactional(rollbackFor = Exception.class)
    @RequiresPermissions("user_front:inz_user_front:assignBeans")
    public Result<?> assignBeans(@Valid @RequestBody AdjustBeansDTO adjustBeansDTO) {
        // 参数校验
        if (adjustBeansDTO.getAmount() == 0) {
            return Result.error("调整金额不能为0");
        }
        if (adjustBeansDTO.getIsBatch() && (adjustBeansDTO.getUserIds() == null || adjustBeansDTO.getUserIds().isEmpty())) {
            return Result.error("批量操作必须选择用户");
        }
        if (adjustBeansDTO.getIsBatch() && adjustBeansDTO.getUserIds().size() > 100) {
            return Result.error("单次批量操作不得超过100个用户");
        }

        try {
            // 批量操作
            List<InzUserFront> users = inzUserFrontService.list(
                    new QueryWrapper<InzUserFront>()
                            .lambda()
                            .in(InzUserFront::getId, adjustBeansDTO.getUserIds())
            );

            if (users.size() != adjustBeansDTO.getUserIds().size()) {
                return Result.error("部分用户不存在，请检查ID列表");
            }

            users.forEach(user -> {
                int originalBeans = user.getGoldenBean();
                int adjustedAmount = adjustBeansDTO.getAmount();

                // 扣减逻辑（amount为负数时）
                if (adjustedAmount < 0) {
                    if (originalBeans + adjustedAmount < 0) {
                        // 金豆不足则归零
                        adjustedAmount = -originalBeans;
                    }
                }

                // 更新用户金豆
                int newBalance = originalBeans + adjustedAmount;
                user.setGoldenBean(newBalance);
                inzUserFrontService.updateById(user);

                // 记录操作日志
                InzUserPayLog log = new InzUserPayLog();
                log.setUserId(user.getId());
                log.setGoldenBean(adjustedAmount); // 记录实际调整值
                log.setType(adjustedAmount > 0 ? 1 : 0); // 1:增加 0:减少
                log.setContent(String.format(
                        "系统调整金豆 %s%d (原余额:%d → 新余额:%d)",
                        adjustedAmount > 0 ? "+" : "",
                        adjustedAmount,
                        originalBeans,
                        newBalance
                ));
                log.setCreateTime(new Date());
                inzUserPayLogService.save(log);
            });
            return Result.ok(adjustBeansDTO.getIsBatch() ?
                    String.format("成功调整%d位用户的金豆", adjustBeansDTO.getUserIds().size()) :
                    "金豆调整成功");
        } catch (Exception e) {
            log.error("金豆调整失败", e);
            return Result.error("系统错误，操作失败");
        }
    }

    /**
     * 修改密码
     */
    @AutoLog(value = "用户表-修改密码")
    @ApiOperation(value = "用户表-修改密码", notes = "用户表-修改密码")
    @RequiresPermissions("user_front:inz_user_front:updatePassword")
    @PutMapping(value = "/updatePassword")
    public Result<?> changePassword(@RequestBody ChangePasswordDTO changePasswordDTO) {
        return inzUserFrontService.changePassword(changePasswordDTO) ? Result.ok("密码修改成功") : Result.error("密码修改失败");
    }

    /**
     * 通过手机号获取后台用户的角色代码列表
     */
    private List<String> getUserRoleCodesByPhone(String phone) {
        try {
            // 1. 通过手机号在sys_user表中查找用户
            LambdaQueryWrapper<SysUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.eq(SysUser::getPhone, phone);
            SysUser sysUser = sysUserService.getOne(userQuery);

            if (sysUser == null) {
                log.warn("未找到手机号对应的后台用户: {}", phone);
                return new ArrayList<>();
            }

            // 2. 通过user_id在sys_user_role表中查找角色ID列表
            LambdaQueryWrapper<SysUserRole> userRoleQuery = new LambdaQueryWrapper<>();
            userRoleQuery.eq(SysUserRole::getUserId, sysUser.getId());
            List<SysUserRole> userRoles = sysUserRoleService.list(userRoleQuery);

            if (userRoles.isEmpty()) {
                log.warn("用户没有分配角色 - 用户ID: {}, 手机号: {}", sysUser.getId(), phone);
                return new ArrayList<>();
            }

            // 3. 获取角色ID列表
            List<String> roleIds = userRoles.stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());

            // 4. 通过role_id在sys_role表中查找角色信息
            LambdaQueryWrapper<SysRole> roleQuery = new LambdaQueryWrapper<>();
            roleQuery.in(SysRole::getId, roleIds);
            List<SysRole> roles = sysRoleService.list(roleQuery);

            // 5. 返回角色代码列表
            List<String> roleCodes = roles.stream()
                    .map(SysRole::getRoleCode)
                    .collect(Collectors.toList());

            log.info("用户角色查询成功 - 手机号: {}, 用户ID: {}, 角色代码: {}", phone, sysUser.getId(), roleCodes);
            return roleCodes;

        } catch (Exception e) {
            log.error("获取用户角色失败 - 手机号: " + phone, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户的角色代码列表（通过用户ID）
     */
    private List<String> getUserRoleCodes(String userId) {
        try {
            // 1. 通过user_id在sys_user_role表中查找角色ID列表
            LambdaQueryWrapper<SysUserRole> userRoleQuery = new LambdaQueryWrapper<>();
            userRoleQuery.eq(SysUserRole::getUserId, userId);
            List<SysUserRole> userRoles = sysUserRoleService.list(userRoleQuery);

            if (userRoles.isEmpty()) {
                return new ArrayList<>();
            }

            // 2. 获取角色ID列表
            List<String> roleIds = userRoles.stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());

            // 3. 通过role_id在sys_role表中查找角色代码
            LambdaQueryWrapper<SysRole> roleQuery = new LambdaQueryWrapper<>();
            roleQuery.in(SysRole::getId, roleIds);
            List<SysRole> roles = sysRoleService.list(roleQuery);

            // 4. 返回角色代码列表
            return roles.stream()
                    .map(SysRole::getRoleCode)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取用户角色失败 - 用户ID: " + userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 判断是否为系统管理员
     */
    private boolean isSystemAdmin(SysUser sysUser) {
        List<String> roleCodes = getUserRoleCodes(sysUser.getId());

        // 只有admin才是系统管理员
        return roleCodes.contains("admin") || "admin".equals(sysUser.getUsername());
    }

    /**
     * 判断是否为代理商用户
     */
    private boolean isAgentUser(SysUser sysUser) {
        List<String> roleCodes = getUserRoleCodes(sysUser.getId());

        // 判断是否包含代理商相关角色
        return roleCodes.contains("chuang") ||           // 创总
                roleCodes.contains("channel") ||         // 渠道
                roleCodes.contains("area_partner") ||    // 区/县合伙人
                roleCodes.contains("city_partner") ||    // 市级合伙人
                roleCodes.contains("province_partner") || // 省级合伙人
                roleCodes.contains("coach") ||           // 教练（如果还在使用）
                roleCodes.contains("partner") ||         // 合伙人（如果还在使用）
                roleCodes.contains("agent");             // 代理商（如果还在使用）
    }

    /**
     * 根据角色代码列表判断是否为系统管理员
     */
    private boolean isSystemAdminByRoleCodes(List<String> roleCodes) {
        return roleCodes.contains("admin");  // 只有admin才是系统管理员
    }

    /**
     * 根据角色代码列表判断是否为代理商用户
     */
    private boolean isAgentUserByRoleCodes(List<String> roleCodes) {
        return roleCodes.contains("chuang") ||           // 创总
                roleCodes.contains("channel") ||         // 渠道
                roleCodes.contains("area_partner") ||    // 区/县合伙人
                roleCodes.contains("city_partner") ||    // 市级合伙人
                roleCodes.contains("province_partner") || // 省级合伙人
                roleCodes.contains("coach") ||           // 教练（如果还在使用）
                roleCodes.contains("partner") ||         // 合伙人（如果还在使用）
                roleCodes.contains("agent");             // 代理商（如果还在使用）
    }

    /**
     * 根据角色代码列表获取用户角色描述
     */
    private String getUserRoleDescriptionByRoleCodes(List<String> roleCodes) {
        if (roleCodes.contains("admin")) return "系统管理员";
        if (roleCodes.contains("chuang")) return "创总";
        if (roleCodes.contains("channel")) return "渠道";
        if (roleCodes.contains("area_partner")) return "区/县合伙人";
        if (roleCodes.contains("city_partner")) return "市级合伙人";
        if (roleCodes.contains("province_partner")) return "省级合伙人";
        if (roleCodes.contains("coach")) return "教练";
        if (roleCodes.contains("partner")) return "合伙人";
        if (roleCodes.contains("agent")) return "代理商";

        return "普通用户";
    }

    /**
     * 获取用户角色描述（兼容旧方法）
     */
    private String getUserRoleDescription(SysUser sysUser) {
        List<String> roleCodes = getUserRoleCodes(sysUser.getId());
        return getUserRoleDescriptionByRoleCodes(roleCodes);
    }

    /**
     * 获取代理商推荐的所有用户ID（直接+间接）
     */
    private List<String> getReferralUserIds(String agentId) {
        try {
            List<String> userIds = new ArrayList<>();

            // 获取直接推荐用户
            List<AgentReferralVO> directReferrals = inzUserFrontService.getDirectReferrals(agentId);
            userIds.addAll(directReferrals.stream().map(AgentReferralVO::getId).collect(Collectors.toList()));

            // 获取间接推荐用户
            List<AgentReferralVO> indirectReferrals = inzUserFrontService.getIndirectReferrals(agentId);
            userIds.addAll(indirectReferrals.stream().map(AgentReferralVO::getId).collect(Collectors.toList()));

            return userIds;
        } catch (Exception e) {
            log.error("获取推荐用户ID失败 - 代理商ID: " + agentId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取代理商金币统计信息
     */
    @ApiOperation(value = "获取代理商金币统计信息", notes = "获取代理商金币统计信息")
    @GetMapping(value = "/getGoldenBeanStats")
    public Result<GoldenBeanStatsVO> getGoldenBeanStats(HttpServletRequest request) {
        try {
            // 1. 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            log.info("获取金币统计信息 - 用户ID: {}, 用户名: {}", loginUser.getId(), loginUser.getUsername());

            // 2. 根据登录用户信息查找sys_user
            SysUser sysUser = sysUserService.getById(loginUser.getId());
            if (sysUser == null) {
                return Result.error("用户信息不存在");
            }

            // 3. 通过手机号获取用户角色信息
            List<String> roleCodes = getUserRoleCodesByPhone(sysUser.getPhone());

            if (roleCodes.isEmpty()) {
                log.warn("用户没有角色权限 - 用户: {}, 手机号: {}", sysUser.getUsername(), sysUser.getPhone());
                return Result.error("您没有相应的角色权限，请联系管理员");
            }

            // 4. 权限验证：只有代理商可以查看金币统计
            if (!isAgentUserByRoleCodes(roleCodes)) {
                log.warn("非代理商用户尝试查看金币统计 - 用户: {}, 角色: {}", sysUser.getUsername(), roleCodes);
                return Result.error("您没有权限查看金币统计信息");
            }

            String roleDescription = getUserRoleDescriptionByRoleCodes(roleCodes);
            log.info("代理商查看金币统计 - 用户: {}, 手机号: {}, 角色: {}",
                    sysUser.getUsername(), sysUser.getPhone(), roleDescription);

            // 5. 获取前台用户信息
            InzUserFront userFront = inzUserFrontService.getOne(
                    new QueryWrapper<InzUserFront>().lambda().eq(InzUserFront::getPhone, sysUser.getPhone())
            );

            if (userFront == null) {
                return Result.error("未找到对应的代理商账户");
            }

            // 6. 获取金币统计信息
            GoldenBeanStatsVO stats = inzUserFrontService.getGoldenBeanStats(userFront.getId());

            log.info("金币统计查询成功 - 用户: {}, 总计: {}, 使用: {}, 剩余: {}",
                    sysUser.getUsername(), stats.getTotalGoldenBean(),
                    stats.getUsedGoldenBean(), stats.getRemainingGoldenBean());

            return Result.OK(stats);

        } catch (Exception e) {
            log.error("获取金币统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 构建当前用户信息
     */
    private CurrentUserInfoVO buildCurrentUserInfo(SysUser sysUser, List<String> roleCodes) {
        CurrentUserInfoVO userInfo = new CurrentUserInfoVO();

        // 基础信息
        userInfo.setUserId(sysUser.getId());
        userInfo.setUsername(sysUser.getUsername());
        userInfo.setRealname(sysUser.getRealname());
        userInfo.setPhone(sysUser.getPhone());

        // 角色信息
        if (!roleCodes.isEmpty()) {
            userInfo.setRoleCode(roleCodes.get(0)); // 主要角色代码
            userInfo.setRoleDescription(getUserRoleDescriptionByRoleCodes(roleCodes));
        } else {
            userInfo.setRoleCode("");
            userInfo.setRoleDescription("普通用户");
        }

        // 权限信息
        userInfo.setIsAdmin(isSystemAdminByRoleCodes(roleCodes));
        userInfo.setIsAgent(isAgentUserByRoleCodes(roleCodes));

        // 权限列表
        List<String> permissions = buildUserPermissions(roleCodes);
        userInfo.setPermissions(permissions);

        return userInfo;
    }

    /**
     * 构建用户权限列表
     */
    private List<String> buildUserPermissions(List<String> roleCodes) {
        List<String> permissions = new ArrayList<>();

        if (isSystemAdminByRoleCodes(roleCodes)) {
            permissions.add("view_all_users");
            permissions.add("manage_users");
            permissions.add("system_admin");
        }

        if (isAgentUserByRoleCodes(roleCodes)) {
            permissions.add("view_referrals");
            permissions.add("manage_golden_bean");
            permissions.add("open_courses");
        }

        // 基础权限
        permissions.add("view_profile");

        return permissions;
    }
}